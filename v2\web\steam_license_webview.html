<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Steam Tools - License Activation</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.min.js"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500;600&display=swap" rel="stylesheet">
    
    <style>
        /* Import Steam Theme */
        :root {
            --background: #0e1419;
            --foreground: #c7d5e0;
            --card: #1b2838;
            --card-foreground: #c7d5e0;
            --popover: #1b2838;
            --popover-foreground: #c7d5e0;
            --primary: #66c0f4;
            --primary-foreground: #0e1419;
            --secondary: #2a475e;
            --secondary-foreground: #c7d5e0;
            --muted: #171a21;
            --muted-foreground: #8b98a5;
            --accent: #4c6b22;
            --accent-foreground: #c7d5e0;
            --destructive: #cd412b;
            --destructive-foreground: #ffffff;
            --border: #2a475e;
            --input: #1b2838;
            --ring: #66c0f4;
            --success: #00d26a;
            --warning: #ffb800;
            --font-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            --font-mono: 'JetBrains Mono', 'Fira Code', monospace;
            --radius: 0.375rem;
            --steam-gradient-primary: linear-gradient(135deg, #66c0f4 0%, #4c9eff 100%);
            --steam-gradient-secondary: linear-gradient(135deg, #2a475e 0%, #1b2838 100%);
            --steam-gradient-success: linear-gradient(135deg, #4c6b22 0%, #5c7e10 100%);
            --steam-gradient-card: linear-gradient(145deg, #1b2838 0%, #16202d 100%);
            --steam-glow: 0 0 20px rgba(102, 192, 244, 0.3);
            --success-glow: 0 0 15px rgba(0, 210, 106, 0.4);
            --titlebar-height: 48px; /* single source of truth for title bar height */
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: var(--font-sans) !important;
            background: var(--background) !important;
            color: var(--foreground) !important;
            height: 100vh;
            overflow: hidden; /* Disable all scrolling */
            margin: 0;
            padding: 0;
        }

        /* Steam-inspired animations */
        @keyframes steamGlow {
            0%, 100% { box-shadow: var(--steam-glow); }
            50% { box-shadow: 0 0 30px rgba(102, 192, 244, 0.5); }
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(30px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        @keyframes progressFill {
            from { width: 0%; }
            to { width: var(--progress-width, 0%); }
        }

        @keyframes pulse {
            0%, 100% { opacity: 0.6; }
            50% { opacity: 1; }
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        @keyframes connectionPulse {
            0%, 100% { 
                box-shadow: 0 0 0 0 rgba(0, 210, 106, 0.7);
                transform: scale(1);
            }
            50% { 
                box-shadow: 0 0 0 8px rgba(0, 210, 106, 0);
                transform: scale(1.05);
            }
        }

        @keyframes dataFlow {
            0% { transform: translateX(-100%) scaleX(0); }
            50% { transform: translateX(0%) scaleX(1); }
            100% { transform: translateX(100%) scaleX(0); }
        }

        @keyframes fadeInScale {
            from {
                opacity: 0;
                transform: scale(0.8);
            }
            to {
                opacity: 1;
                transform: scale(1);
            }
        }

        /* Custom Title Bar */
        .custom-title-bar {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: var(--titlebar-height);
            background: var(--steam-gradient-secondary);
            border-bottom: 1px solid var(--border);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 15px;
            z-index: 10000;
            user-select: none;
        }

        .title-bar-drag-region {
            flex: 1;
            height: 100%;
            display: flex;
            align-items: center;
            -webkit-app-region: drag; /* Allow native dragging in this area */
            z-index: 1; /* Ensure it does not overlay controls */
        }

        .title-bar-left {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .title-bar-icon {
            font-size: 16px;
            color: var(--primary);
        }

        .title-bar-title {
            font-size: 14px;
            font-weight: 600;
            color: var(--foreground);
        }

        .title-bar-controls {
            display: flex;
            gap: 5px;
            -webkit-app-region: no-drag; /* Ensure buttons receive clicks */
            position: relative;
            z-index: 2; /* Sit above drag region */
            pointer-events: auto;
        }

        .title-bar-btn {
            width: 30px;
            height: 25px;
            border: none;
            background: transparent;
            color: var(--text-secondary);
            font-size: 14px;
            font-weight: bold;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 3px;
            transition: all 0.2s ease;
            -webkit-app-region: no-drag; /* Explicitly mark buttons as non-drag */
            pointer-events: auto;
        }

        .title-bar-btn:hover {
            background: var(--surface-hover);
            color: var(--foreground);
        }

        .title-bar-btn.close:hover {
            background: var(--destructive);
            color: white;
        }

        /* Close button spinner state */
        .title-bar-btn.loading {
            cursor: wait;
        }
        .title-bar-btn .spinner {
            width: 14px;
            height: 14px;
            border: 2px solid rgba(255, 255, 255, 0.35);
            border-top-color: #fff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            display: inline-block;
        }
        .title-bar-btn.close.loading {
            background: var(--destructive);
            color: white;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        /* Main container - adjusted for title bar */
        .steam-container {
            background: var(--background);
            height: auto;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            /* Move the entire container below the fixed title bar */
            margin-top: var(--titlebar-height); /* equal to title bar height */
            padding: 16px 2rem 2rem 2rem;
            position: relative;
            overflow: hidden;
        }

        .steam-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 50% 50%, rgba(102, 192, 244, 0.05) 0%, transparent 70%);
            pointer-events: none;
        }

        /* Connection Status Bar */
        .connection-status {
            position: fixed; /* anchor to viewport to avoid overlap */
            top: calc(var(--titlebar-height) + 16px);
            right: 20px;
            background: var(--steam-gradient-card);
            border: 1px solid var(--border);
            border-radius: var(--radius);
            padding: 12px 16px;
            display: flex;
            align-items: center;
            gap: 12px;
            z-index: 9000; /* below title bar (10000), above content */
            animation: slideInUp 1s ease-out 0.5s both;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }

        .connection-indicator {
            position: relative;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .connection-dot {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background: var(--success);
            animation: connectionPulse 2s infinite;
            box-shadow: var(--success-glow);
        }

        .connection-dot.connecting {
            background: var(--warning);
            animation: pulse 1s infinite;
            box-shadow: 0 0 15px rgba(255, 184, 0, 0.4);
        }

        .connection-dot.disconnected {
            background: var(--destructive);
            animation: none;
            box-shadow: 0 0 15px rgba(205, 65, 43, 0.4);
        }

        .connection-text {
            font-size: 0.875rem;
            font-weight: 500;
            color: var(--foreground);
        }

        .connection-details {
            font-size: 0.75rem;
            color: var(--muted-foreground);
            margin-left: 4px;
        }

        /* Server Status Panel */
        .server-status-panel {
            background: var(--steam-gradient-secondary);
            border: 1px solid var(--border);
            border-radius: var(--radius);
            padding: 1rem;
            margin-bottom: 1.5rem;
            animation: fadeInScale 0.6s ease-out 0.3s both;
        }

        .server-status-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 0.75rem;
        }

        .server-status-title {
            font-size: 0.875rem;
            font-weight: 600;
            color: var(--foreground);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .server-icon {
            width: 16px;
            height: 16px;
            color: var(--primary);
        }

        .server-metrics {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin-top: 0.75rem;
        }

        .metric-item {
            display: flex;
            flex-direction: column;
            gap: 0.25rem;
        }

        .metric-label {
            font-size: 0.75rem;
            color: var(--muted-foreground);
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .metric-value {
            font-size: 0.875rem;
            font-weight: 600;
            color: var(--success);
            font-family: var(--font-mono);
        }

        .data-flow-indicator {
            height: 2px;
            background: var(--muted);
            border-radius: 1px;
            overflow: hidden;
            position: relative;
            margin-top: 0.5rem;
        }

        .data-flow-bar {
            height: 100%;
            background: linear-gradient(90deg, transparent, var(--primary), transparent);
            animation: dataFlow 2s infinite;
            border-radius: 1px;
        }

        /* Header */
        .steam-header {
            text-align: center;
            margin-bottom: 3rem;
            animation: slideInUp 0.8s ease-out;
        }

        .steam-title {
            font-size: 2.5rem !important;
            font-weight: 700 !important;
            color: var(--primary) !important;
            margin-bottom: 0.5rem;
            text-shadow: 0 0 20px rgba(102, 192, 244, 0.3);
        }

        .steam-subtitle {
            font-size: 1.125rem !important;
            color: var(--muted-foreground) !important;
            font-weight: 400 !important;
        }

        /* Main card */
        .steam-card {
            background: var(--steam-gradient-card);
            border: 1px solid var(--border);
            border-radius: var(--radius);
            padding: 3rem;
            width: 100%;
            max-width: 500px;
            box-shadow: 0 20px 25px 0 rgba(0, 0, 0, 0.5), 0 10px 10px 0 rgba(0, 0, 0, 0.4);
            animation: slideInUp 0.8s ease-out 0.2s both;
            position: relative;
        }

        .steam-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, var(--primary), transparent);
            opacity: 0.5;
        }

        /* Logo area */
        .steam-logo {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
            margin-bottom: 2rem;
            padding: 2rem;
            background: var(--steam-gradient-secondary);
            border-radius: var(--radius);
            border: 1px solid var(--border);
            gap: 1rem;
        }

        .steam-logo-icon {
            width: 64px !important;
            height: 64px !important;
            color: var(--primary) !important;
            filter: drop-shadow(var(--steam-glow));
            flex-shrink: 0;
            display: block;
        }

        .steam-logo-text {
            font-size: 1.5rem !important;
            font-weight: 600 !important;
            color: var(--primary) !important;
            margin: 0 !important;
            line-height: 1.2 !important;
            display: block;
        }

        /* License input */
        .license-section {
            margin-bottom: 2rem;
        }

        .license-label {
            display: block;
            font-size: 0.875rem !important;
            font-weight: 500 !important;
            color: var(--foreground) !important;
            margin-bottom: 0.5rem;
        }

        .license-input {
            width: 100%;
            padding: 1rem;
            background: var(--input) !important;
            border: 2px solid var(--border) !important;
            border-radius: var(--radius) !important;
            color: var(--foreground) !important;
            font-family: var(--font-mono) !important;
            font-size: 1.125rem !important;
            text-align: center;
            letter-spacing: 0.1em;
            transition: all 0.3s ease;
        }

        .license-input:focus {
            outline: none !important;
            border-color: var(--primary) !important;
            box-shadow: var(--steam-glow), 0 0 0 3px rgba(102, 192, 244, 0.1) !important;
            transform: scale(1.02);
        }

        .license-input.error {
            border-color: var(--destructive) !important;
            animation: shake 0.4s ease-in-out;
        }

        .license-input.success {
            border-color: var(--accent) !important;
            box-shadow: 0 0 20px rgba(76, 107, 34, 0.3);
        }

        /* Activate button */
        .activate-btn {
            width: 100%;
            padding: 1rem 2rem;
            background: var(--steam-gradient-primary) !important;
            border: none !important;
            border-radius: var(--radius) !important;
            color: var(--primary-foreground) !important;
            font-size: 1.125rem !important;
            font-weight: 600 !important;
            cursor: pointer;
            transition: all 0.2s ease;
            margin-bottom: 2rem;
            position: relative;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        .activate-btn:hover {
            transform: scale(1.05);
            box-shadow: var(--steam-glow);
        }

        .activate-btn:active {
            transform: scale(0.98);
        }

        .activate-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .activate-btn.loading {
            color: transparent;
        }

        .activate-btn .spinner {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 20px;
            height: 20px;
            border: 2px solid transparent;
            border-top: 2px solid var(--primary-foreground);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            opacity: 0;
        }

        .activate-btn.loading .spinner {
            opacity: 1;
        }

        .btn-content {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            width: 100%;
        }

        .btn-icon {
            width: 20px !important;
            height: 20px !important;
            flex-shrink: 0;
        }

        .btn-text {
            line-height: 1;
        }

        /* Progress bar */
        .progress-section {
            margin-bottom: 2rem;
        }

        .progress-label {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.5rem;
            font-size: 0.875rem !important;
            color: var(--foreground) !important;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: var(--muted);
            border-radius: 4px;
            overflow: hidden;
            position: relative;
        }

        .progress-fill {
            height: 100%;
            background: var(--steam-gradient-primary);
            border-radius: 4px;
            transition: width 0.8s ease-out;
            position: relative;
            overflow: hidden;
        }

        .progress-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        /* HWID section */
        .hwid-section {
            background: var(--steam-gradient-secondary);
            border: 1px solid var(--border);
            border-radius: var(--radius);
            padding: 1.5rem;
            margin-bottom: 2rem;
        }

        .hwid-label {
            font-size: 0.875rem !important;
            font-weight: 500 !important;
            color: var(--muted-foreground) !important;
            margin-bottom: 0.5rem;
        }

        .hwid-value {
            font-family: var(--font-mono) !important;
            font-size: 1rem !important;
            color: var(--foreground) !important;
            word-break: break-all;
            margin-bottom: 1rem;
            padding: 0.75rem;
            background: var(--muted);
            border-radius: calc(var(--radius) - 2px);
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .hwid-value:hover {
            background: var(--input);
            transform: translateY(-1px);
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.875rem !important;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: var(--muted-foreground);
            animation: pulse 2s infinite;
        }

        .status-dot.validating {
            background: var(--primary);
        }

        .status-dot.success {
            background: var(--accent);
            animation: none;
        }

        .status-dot.error {
            background: var(--destructive);
            animation: none;
        }

        /* Support buttons */
        .support-section {
            display: flex;
            gap: 1rem;
            justify-content: center;
        }

        .support-btn {
            padding: 0.75rem 1.5rem;
            background: transparent !important;
            border: 1px solid var(--border) !important;
            border-radius: var(--radius) !important;
            color: var(--muted-foreground) !important;
            font-size: 0.875rem !important;
            cursor: pointer;
            transition: all 0.15s ease;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .support-btn:hover {
            border-color: var(--primary);
            color: var(--primary);
            transform: scale(1.05);
        }

        /* Auto-sizing and responsive design */
        .steam-container {
            min-height: calc(100vh - var(--titlebar-height)); /* Account for title bar */
            max-height: calc(100vh - var(--titlebar-height));
        }

        .steam-card {
            max-height: calc(100vh - 200px); /* Ensure card fits in viewport */
            overflow-y: auto; /* Allow internal scrolling if needed */
        }

        /* Responsive design */
        @media (max-width: 640px) {
            .steam-container {
                padding: 16px 1rem 1rem 1rem;
            }

            .steam-card {
                padding: 1.5rem;
                max-height: calc(100vh - 150px);
            }

            .steam-title {
                font-size: 1.8rem !important;
            }

            .support-section {
                flex-direction: column;
            }

            .connection-status {
                position: relative;
                top: auto;
                right: auto;
                margin-bottom: 1rem;
                align-self: stretch;
            }

            .server-metrics {
                grid-template-columns: 1fr;
            }
        }

        /* Ensure no scrollbars appear */
        ::-webkit-scrollbar {
            width: 0px;
            background: transparent;
        }

        html, body {
            overflow: hidden !important;
        }

        /* Toast container - bottom-right stack, positioned within steam-container */
        #toastContainer {
            position: absolute; /* respect .steam-container padding */
            bottom: 2rem;
            right: 2rem;
            z-index: 20000; /* Above title bar (10000) */
            pointer-events: none; /* Let individual toasts handle clicks */
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            gap: 0.5rem;
        }

        /* Toast notifications - stacked inside container */
        .toast {
            position: relative;
            background: var(--card);
            border: 1px solid var(--border);
            border-radius: var(--radius);
            padding: 1rem 1.5rem;
            color: var(--foreground);
            box-shadow: 0 10px 15px 0 rgba(0, 0, 0, 0.5);
            transform: translateY(10px);
            opacity: 0;
            transition: transform 0.2s ease, opacity 0.2s ease;
            pointer-events: auto;
        }

        .toast.show {
            transform: translateY(0);
            opacity: 1;
        }

        .toast.success {
            border-color: var(--accent);
            background: linear-gradient(135deg, var(--accent) 0%, var(--card) 100%);
        }

        .toast.error {
            border-color: var(--destructive);
            background: linear-gradient(135deg, var(--destructive) 0%, var(--card) 100%);
        }
        
        /* Admin Panel Overlay */
        .admin-panel {
            position: fixed;
            top: var(--titlebar-height);
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.55);
            backdrop-filter: blur(2px);
            display: none;
            z-index: 10001; /* Above title bar and content */
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .admin-panel.show { display: flex; }

        .admin-panel-content {
            width: min(900px, 90vw);
            max-height: min(80vh, 700px);
            border-radius: var(--radius);
            border: 1px solid var(--border);
            background: var(--steam-gradient-card);
            box-shadow: var(--steam-glow);
            overflow: hidden;
            color: var(--foreground);
        }

        .admin-panel-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 14px;
            border-bottom: 1px solid var(--border);
            background: var(--steam-gradient-secondary);
        }

        .admin-panel-title {
            font-weight: 600;
            font-size: 14px;
        }

        .admin-panel-close {
            -webkit-app-region: no-drag;
            border: 0;
            background: transparent;
            color: var(--foreground);
            width: 28px;
            height: 28px;
            border-radius: 4px;
            cursor: pointer;
        }

        .admin-panel-close:hover {
            background: rgba(255, 255, 255, 0.08);
        }

        .admin-panel-body {
            padding: 14px;
            display: grid;
            gap: 10px;
        }

        .admin-info {
            font-family: var(--font-mono);
            font-size: 12px;
            color: var(--muted-foreground);
            background: var(--muted);
            border: 1px solid var(--border);
            border-radius: var(--radius);
            padding: 10px;
            max-height: 280px;
            overflow: auto;
        }
    </style>
</head>
<body>
    <!-- Custom Title Bar -->
    <div class="custom-title-bar">
        <div class="title-bar-drag-region pywebview-drag-region">
            <div class="title-bar-left">
                <div class="title-bar-icon">🎮</div>
                <div class="title-bar-title">Steam Tools - License Activation</div>
            </div>
        </div>
        <div class="title-bar-controls">
            <button class="title-bar-btn minimize" onclick="minimizeApp(event)" title="Minimize" id="minimizeBtn">−</button>
            <button class="title-bar-btn close" onclick="closeApp(event)" title="Close" id="closeBtn">✕</button>
        </div>
    </div>

    <div class="steam-container">
        <!-- Toast container for runtime notifications (inside container so padding applies) -->
        <div id="toastContainer" aria-live="polite" aria-atomic="true"></div>
        <!-- Connection Status -->
        <div class="connection-status" id="connectionStatus">
            <div class="connection-indicator">
                <div class="connection-dot" id="connectionDot"></div>
                <div>
                    <span class="connection-text" id="connectionText">Connected to License Server</span>
                    <div class="connection-details" id="connectionDetails">auth.steamtools.com • 24ms</div>
                </div>
            </div>
        </div>

        <!-- Header -->
        <div class="steam-header">
            <h1 class="steam-title">🎮 STEAM TOOLS</h1>
            <p class="steam-subtitle">License Activation</p>
        </div>

        <!-- Main Card -->
        <div class="steam-card">
            <!-- Server Status Panel -->
            <div class="server-status-panel">
                <div class="server-status-header">
                    <div class="server-status-title">
                        <i data-lucide="server" class="server-icon"></i>
                        Authentication Server
                    </div>
                    <div class="connection-dot" id="serverStatusDot"></div>
                </div>
                <div class="server-metrics">
                    <div class="metric-item">
                        <div class="metric-label">Latency</div>
                        <div class="metric-value" id="latencyValue">24ms</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-label">Uptime</div>
                        <div class="metric-value" id="uptimeValue">99.9%</div>
                    </div>
                </div>
                <div class="data-flow-indicator">
                    <div class="data-flow-bar"></div>
                </div>
            </div>

            <!-- Logo Area -->
            <div class="steam-logo">
                <i data-lucide="gamepad-2" class="steam-logo-icon"></i>
                <div class="steam-logo-text">Steam Tools</div>
            </div>

            <!-- License Input -->
            <div class="license-section">
                <label class="license-label">License Key</label>
                <input 
                    type="text" 
                    class="license-input" 
                    id="licenseInput"
                    placeholder="XXXX-XXXX-XXXX"
                >
            </div>

            <!-- Activate Button -->
            <button class="activate-btn" id="activateBtn">
                <div class="btn-content">
                    <span class="btn-text">Activate License</span>
                </div>
                <div class="spinner"></div>
            </button>

            <!-- Progress Bar -->
            <div class="progress-section">
                <div class="progress-label">
                    <span>Activation Progress</span>
                    <span id="progressText">0%</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill" style="width: 0%"></div>
                </div>
            </div>

            <!-- HWID Section -->
            <div class="hwid-section">
                <div class="hwid-label">Hardware ID</div>
                <div class="hwid-value" id="hwidValue" onclick="copyHWID()">
                    ABC123-DEF456-GHI789-JKL012
                </div>
                <div class="status-indicator">
                    <div class="status-dot" id="statusDot"></div>
                    <span id="statusText">Ready</span>
                </div>
            </div>

            <!-- Support Buttons -->
            <div class="support-section">
                <a href="#" class="support-btn">
                    <i data-lucide="help-circle" style="width: 16px; height: 16px;"></i>
                    Help
                </a>
                <a href="#" class="support-btn">
                    <i data-lucide="message-circle" style="width: 16px; height: 16px;"></i>
                    Support
                </a>
            </div>
        </div>
    </div>

    <!-- Admin Control Panel Overlay -->
    <div id="adminPanel" class="admin-panel" aria-hidden="true">
        <div class="admin-panel-content">
            <div class="admin-panel-header">
                <div class="admin-panel-title">Admin Control Panel</div>
                <button class="admin-panel-close" onclick="hideAdminPanel()" title="Close">✕</button>
            </div>
            <div class="admin-panel-body">
                <p>Use this panel for administrative utilities. Press <strong>Ctrl+Shift+A</strong> to toggle.</p>
                <div id="adminInfo" class="admin-info">Loading system info...</div>
            </div>
        </div>
    </div>

    <!-- Toast Container -->
    <div id="toastContainer"></div>

    <!-- PyWebView JavaScript API -->

    <script>
        // Initialize Lucide icons
        lucide.createIcons();

        // Connection status elements
        const connectionDot = document.getElementById('connectionDot');
        const connectionText = document.getElementById('connectionText');
        const connectionDetails = document.getElementById('connectionDetails');
        const serverStatusDot = document.getElementById('serverStatusDot');
        const latencyValue = document.getElementById('latencyValue');
        const uptimeValue = document.getElementById('uptimeValue');

        // License input elements
        const licenseInput = document.getElementById('licenseInput');
        const activateBtn = document.getElementById('activateBtn');
        const progressFill = document.getElementById('progressFill');
        const progressText = document.getElementById('progressText');
        const statusDot = document.getElementById('statusDot');
        const statusText = document.getElementById('statusText');

        // Initialize application only after the PyWebView bridge is ready
        (function deferInitialize() {
            if (window.pywebview && window.pywebview.api) {
                initializeApp();
            } else {
                document.addEventListener('pywebviewready', () => initializeApp(), { once: true });
            }
        })();

        // Initialize application with real backend data (PyWebView)
        async function initializeApp() {
            try {
                console.log('Initializing PyWebView app...');

                // Test PyWebView API connection
                if (typeof pywebview === 'undefined') {
                    console.error('PyWebView API not available');
                    showToast('PyWebView API not available', 'error');
                    return;
                }

                console.log('PyWebView API available, testing connection...');

                // Get system info and HWID using PyWebView API
                const systemInfo = await pywebview.api.get_system_info();
                console.log('System info:', systemInfo);

                const hwid = await pywebview.api.get_hwid();
                console.log('HWID:', hwid);

                // Update HWID display
                document.getElementById('hwidValue').textContent = hwid;

                // Check connection status
                updateConnectionStatus();

                // Check Steam status
                checkSteamStatus();

                // Set up periodic updates
                setInterval(updateConnectionStatus, 5000);
                setInterval(checkSteamStatus, 3000);

                console.log('App initialization complete');
                showToast('Application initialized successfully', 'success');

            } catch (error) {
                console.error('Failed to initialize app:', error);
                showToast('Failed to initialize application: ' + error.message, 'error');
            }
        }

        // Update connection status with real backend data (PyWebView)
        async function updateConnectionStatus() {
            try {
                const status = await pywebview.api.get_connection_status();

                if (status.connected) {
                    connectionDot.classList.remove('connecting', 'disconnected');
                    serverStatusDot.classList.remove('connecting', 'disconnected');
                    connectionText.textContent = 'Connected to License Server';
                    connectionDetails.textContent = `${status.server} • ${status.latency}`;
                    latencyValue.textContent = status.latency;
                    uptimeValue.textContent = status.uptime;
                } else {
                    connectionDot.classList.add('disconnected');
                    serverStatusDot.classList.add('disconnected');
                    connectionText.textContent = 'Connection Failed';
                    connectionDetails.textContent = 'Unable to reach server';
                    latencyValue.textContent = 'N/A';
                    uptimeValue.textContent = 'N/A';
                }
            } catch (error) {
                console.error('Connection status check failed:', error);
                connectionDot.classList.add('disconnected');
                serverStatusDot.classList.add('disconnected');
                connectionText.textContent = 'Connection Error';
                connectionDetails.textContent = 'Check your internet connection';
            }
        }

        // Check Steam status (PyWebView)
        async function checkSteamStatus() {
            try {
                const steamRunning = await pywebview.api.is_steam_running();
                const isAdmin = await pywebview.api.is_admin();

                // Update status based on Steam state
                if (steamRunning) {
                    updateStatus('warning', 'Steam is running - close Steam to activate');
                } else if (!isAdmin) {
                    updateStatus('warning', 'Administrator privileges recommended');
                } else {
                    updateStatus('ready', 'Ready for activation');
                }
            } catch (error) {
                console.error('Steam status check failed:', error);
            }
        }

        // Add keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // Ctrl+Shift+R to refresh connection
            if (e.ctrlKey && e.shiftKey && e.key === 'R') {
                e.preventDefault();
                updateConnectionStatus();
                showToast('Connection status refreshed', 'success');
            }

            // Ctrl+Shift+S to check Steam status
            if (e.ctrlKey && e.shiftKey && e.key === 'S') {
                e.preventDefault();
                checkSteamStatus();
                showToast('Steam status checked', 'success');
            }
            // Ctrl+Shift+A to toggle Admin panel
            if (e.ctrlKey && e.shiftKey && (e.key === 'A' || e.key === 'a')) {
                e.preventDefault();
                const panel = document.getElementById('adminPanel');
                if (panel && panel.classList.contains('show')) {
                    hideAdminPanel();
                } else {
                    showAdminPanel();
                }
            }
        });

        // Format license key input with real validation
        licenseInput.addEventListener('input', async function(e) {
            const value = e.target.value; // Do not enforce format or length

            if (value.trim().length > 0) {
                try {
                    const validation = await pywebview.api.validate_license_key(value);
                    e.target.classList.toggle('success', !!validation.valid);
                    e.target.classList.toggle('error', !validation.valid);
                    // Do not disable the button based on format/length; allow activation attempt
                    activateBtn.disabled = false;

                    if (!validation.valid) {
                        updateStatus('warning', validation.message || 'License key format not recognized');
                    } else {
                        updateStatus('ready', 'License key recognized');
                    }
                } catch (error) {
                    console.error('License validation failed:', error);
                    e.target.classList.remove('success');
                    e.target.classList.remove('error');
                    // Keep button enabled so user can still attempt activation
                    activateBtn.disabled = false;
                }
            } else {
                e.target.classList.remove('success', 'error');
                activateBtn.disabled = true;
                updateStatus('ready', 'Enter license key');
            }
        });

        // Real activation process
        activateBtn.addEventListener('click', async function() {
            if (this.disabled) return;

            const licenseKey = licenseInput.value.trim();
            if (!licenseKey) {
                showToast('Please enter a license key', 'error');
                return;
            }

            this.disabled = true;
            this.classList.add('loading');

            updateStatus('validating', 'Checking Steam status...');
            updateProgress(10);

            try {
                // Check if Steam is running
                const steamRunning = await pywebview.api.is_steam_running();
                if (steamRunning) {
                    updateStatus('warning', 'Steam is running - attempting to close...');
                    updateProgress(20);

                    const closeResult = await pywebview.api.close_steam_processes();
                    if (!closeResult.success) {
                        throw new Error(`Failed to close Steam: ${closeResult.error}`);
                    }

                    showToast(`Closed ${closeResult.count} Steam process(es)`, 'success');
                    updateProgress(40);
                }

                updateStatus('validating', 'Activating license...');
                updateProgress(60);

                // Activate license
                const result = await pywebview.api.activate_license(licenseKey);
                updateProgress(80);

                if (result.success) {
                    updateProgress(100);
                    updateStatus('success', result.message);

                    // Update button to show success
                    this.classList.remove('loading');
                    this.innerHTML = `
                        <div class="btn-content">
                            <i data-lucide="check" class="btn-icon"></i>
                            <span class="btn-text">Activated</span>
                        </div>
                    `;
                    this.style.background = 'var(--steam-gradient-success)';
                    showToast(result.message, 'success');
                    lucide.createIcons();
                } else {
                    throw new Error(result.message);
                }

            } catch (error) {
                console.error('Activation failed:', error);
                updateStatus('error', error.message);
                updateProgress(0);

                this.disabled = false;
                this.classList.remove('loading');
                showToast(error.message, 'error');
            }
        });

        // Copy HWID to clipboard (PyWebView)
        async function copyHWID() {
            try {
                const hwid = await pywebview.api.get_hwid();
                await navigator.clipboard.writeText(hwid);
                showToast('Hardware ID copied to clipboard!', 'success');
            } catch (error) {
                console.error('Failed to copy HWID:', error);
                showToast('Failed to copy Hardware ID', 'error');
            }
        }

        function updateProgress(percent) {
            progressFill.style.width = percent + '%';
            progressText.textContent = percent + '%';
        }

        function updateStatus(type, message) {
            statusDot.className = 'status-dot ' + type;
            statusText.textContent = message;
        }

        // Update HWID display onclick
        document.getElementById('hwidValue').onclick = copyHWID;

        function showToast(message, type = 'info') {
            const toast = document.createElement('div');
            toast.className = `toast ${type}`;
            toast.innerHTML = `
                <div style="display: flex; align-items: center; gap: 0.5rem;">
                    <i data-lucide="${type === 'success' ? 'check-circle' : 'info'}" style="width: 16px; height: 16px;"></i>
                    ${message}
                </div>
            `;
            
            document.getElementById('toastContainer').appendChild(toast);
            lucide.createIcons();
            
            setTimeout(() => toast.classList.add('show'), 100);
            setTimeout(() => {
                toast.classList.remove('show');
                setTimeout(() => toast.remove(), 300);
            }, 3000);
        }

        // Title bar functions (PyWebView)
        async function closeApp(e) {
            try { e && e.stopPropagation && e.stopPropagation(); } catch(_) {}
            try { e && e.preventDefault && e.preventDefault(); } catch(_) {}
            console.log('Close button clicked');
            const closeBtn = document.getElementById('closeBtn');
            if (closeBtn) {
                try {
                    closeBtn.disabled = true;
                    closeBtn.classList.add('loading');
                    closeBtn.setAttribute('aria-busy', 'true');
                    closeBtn.innerHTML = '<span class="spinner" aria-label="Closing..."></span>';
                } catch (_) {}
            }
            try {
                console.log('Calling pywebview.api.close_app()');
                if (window.pywebview && pywebview.api && typeof pywebview.api.close_app === 'function') {
                    await pywebview.api.close_app();
                } else if (window.pywebview && typeof window.pywebview.close === 'function') {
                    window.pywebview.close();
                } else {
                    throw new Error('pywebview API not available');
                }
            } catch (error) {
                console.log('Close error:', error);
                showToast('Closing application...', 'success');
                setTimeout(() => {
                    try {
                        window.close();
                    } catch (e) {
                        window.location.href = 'about:blank';
                    }
                }, 500);
            }
        }

        async function minimizeApp() {
            console.log('Minimize button clicked');

            // Visual feedback
            const minimizeBtn = document.getElementById('minimizeBtn');
            if (minimizeBtn) {
                minimizeBtn.style.background = '#4a5568';
                minimizeBtn.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    minimizeBtn.style.background = '';
                    minimizeBtn.style.transform = '';
                }, 200);
            }

            try {
                console.log('Calling pywebview.api.minimize_app()');
                // Call Python API to minimize
                const result = await pywebview.api.minimize_app();
                console.log('Minimize result:', result);

                if (result && result.success) {
                    showToast('Window minimized', 'success');
                } else {
                    showToast('Minimize not supported', 'warning');
                }

            } catch (error) {
                console.log('Minimize error:', error);
                showToast('Minimize not available', 'warning');
            }
        }

        // Auto-resize based on screen size (PyWebView)
        async function autoResize() {
            try {
                // PyWebView handles sizing automatically, but we can still adjust content
                console.log('Auto-resizing content for PyWebView');

                // Adjust container height to fit perfectly
                const container = document.querySelector('.steam-container');
                const titleBarHeight = 40;
                const availableHeight = window.innerHeight - titleBarHeight;

                container.style.height = `${availableHeight}px`;
                container.style.maxHeight = `${availableHeight}px`;

                // Adjust card size if needed
                const card = document.querySelector('.steam-card');
                const maxCardHeight = availableHeight - 200; // Leave space for header and margins
                card.style.maxHeight = `${maxCardHeight}px`;

            } catch (error) {
                console.error('Failed to get screen info:', error);
            }
        }

        // Window dragging functionality
        let isDragging = false;
        let dragStartX = 0;
        let dragStartY = 0;

        function initWindowDragging() {
            const dragRegion = document.querySelector('.title-bar-drag-region');
            if (!dragRegion) return;

            dragRegion.addEventListener('mousedown', (e) => {
                // Do not start dragging if click originates in controls/buttons
                const inControls = e.target.closest && (e.target.closest('.title-bar-controls') || e.target.closest('.title-bar-btn'));
                if (inControls) return;

                isDragging = true;
                dragStartX = e.clientX;
                dragStartY = e.clientY;
                dragRegion.style.cursor = 'grabbing';

                // Prevent text selection
                e.preventDefault();
            });

            document.addEventListener('mousemove', (e) => {
                if (!isDragging) return;

                const deltaX = e.clientX - dragStartX;
                const deltaY = e.clientY - dragStartY;

                // Move window (this works in Chrome app mode)
                window.moveBy(deltaX, deltaY);

                dragStartX = e.clientX;
                dragStartY = e.clientY;
            });

            document.addEventListener('mouseup', () => {
                if (isDragging) {
                    isDragging = false;
                    dragRegion.style.cursor = 'default'; // Reset cursor on the actual drag region
                }
            });
        }

        // Handle window resize
        function handleResize() {
            autoResize();
        }

        // Prevent context menu on title bar
        function preventContextMenu() {
            const titleBar = document.querySelector('.custom-title-bar');
            titleBar.addEventListener('contextmenu', (e) => {
                e.preventDefault();
            });
        }

        // Initialize everything
        updateStatus('ready', 'Initializing...');
        autoResize();
        initWindowDragging();
        preventContextMenu();

        // Ensure title bar buttons are wired even if inline onclick is ignored
        try {
            const closeBtnEl = document.getElementById('closeBtn');
            const minimizeBtnEl = document.getElementById('minimizeBtn');
            if (closeBtnEl) {
                closeBtnEl.addEventListener('click', (e) => closeApp(e), { capture: true });
            }
            if (minimizeBtnEl) {
                minimizeBtnEl.addEventListener('click', (e) => minimizeApp(e), { capture: true });
            }
            // Expose functions globally for inline handlers
            window.closeApp = closeApp;
            window.minimizeApp = minimizeApp;
        } catch (err) {
            console.log('Failed to wire title bar buttons:', err);
        }

        // Diagnostics: report pywebview bridge availability shortly after load
        setTimeout(() => {
            try {
                const hasBridge = !!(window.pywebview && window.pywebview.api);
                console.log('PyWebView bridge available?', hasBridge, window.pywebview);
            } catch (e) {
                console.log('PyWebView bridge probe error:', e);
            }
        }, 200);

        // Admin panel helpers (exposed for Python API)
        async function populateAdminInfo() {
            try {
                const info = await pywebview.api.get_system_info();
                const hwid = await pywebview.api.get_hwid();
                const isAdmin = await pywebview.api.is_admin();
                const steamRunning = await pywebview.api.is_steam_running();
                const blob = {
                    timestamp: new Date().toISOString(),
                    hwid,
                    isAdmin,
                    steamRunning,
                    system: info
                };
                const el = document.getElementById('adminInfo');
                if (el) {
                    el.textContent = JSON.stringify(blob, null, 2);
                }
            } catch (err) {
                const el = document.getElementById('adminInfo');
                if (el) el.textContent = 'Failed to load admin info: ' + (err && err.message ? err.message : err);
            }
        }

        function showAdminPanel() {
            // Navigate to dedicated Admin webview page
            try { 
                try { 
                    if (window.pywebview && window.pywebview.api && typeof window.pywebview.api.log_event === 'function') {
                        try { window.pywebview.api.log_event('License page navigation: opening Admin Panel'); } catch (_) {}
                    }
                } catch (_) {}
            } catch (_) {}
            try {
                window.location.href = 'admin_webview.html';
            } catch (e) {
                console.log('Failed to open admin page:', e);
            }
        }

        function hideAdminPanel() {
            // No-op on license page (admin is a separate page)
        }

        // Expose globally for Python-side evaluate_js calls
        window.showAdminPanel = showAdminPanel;
        window.hideAdminPanel = hideAdminPanel;

        // Keyboard shortcut: Ctrl+Shift+A -> Admin page
        document.addEventListener('keydown', async (e) => {
            const key = (e.key || '').toUpperCase();
            if (e.ctrlKey && e.shiftKey && key === 'A') {
                e.preventDefault();
                try {
                    if (window.pywebview && window.pywebview.api && typeof window.pywebview.api.log_event === 'function') {
                        await window.pywebview.api.log_event('Shortcut pressed: Ctrl+Shift+A on License page; navigating to Admin Panel');
                    }
                } catch (_) {}
                try { window.location.href = 'admin_webview.html'; } catch(err) { console.log(err); }
            }
        });

        // Handle window resize events
        window.addEventListener('resize', handleResize);

        // Double-click title bar to maximize/restore (if supported)
        document.querySelector('.custom-title-bar').addEventListener('dblclick', (e) => {
            if (e.target.classList.contains('title-bar-btn')) return;

            // Toggle fullscreen (limited support in Chrome app mode)
            if (document.fullscreenElement) {
                document.exitFullscreen();
            } else {
                document.documentElement.requestFullscreen();
            }
        });
    </script>
</body>
</html>