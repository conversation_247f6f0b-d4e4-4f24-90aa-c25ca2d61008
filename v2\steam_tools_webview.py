import webview
import os
import sys
import json
import hashlib
import platform
import psutil
import ctypes
import time
import threading
import requests
import shutil
from datetime import datetime, timezone

# Add parent directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

# Fixed admin password hash (SHA-256). Hidden in program, not stored in config.
# Hash value requested by owner: 240be518fabd2724ddb6f04eeb1da5967448d7e831c08c8fa822809f74c720a9
ADMIN_PASSWORD_HASH = '240be518fabd2724ddb6f04eeb1da5967448d7e831c08c8fa822809f74c720a9'

class SteamToolsAPI:
    def __init__(self):
        self.keyauth_app = None
        self.config = self.load_config()
        # Persist removal of deprecated/forbidden fields (like admin password in config)
        try:
            self.save_config()
        except Exception:
            pass
        self.init_keyauth()
        # Admin session (ephemeral) – 30 minutes default
        self.admin_session_expiry = 0
        self.admin_session_timeout = 30 * 60  # seconds
        
    def show_admin_panel(self):
        """Request the webview to open the Admin panel (if available in JS)."""
        try:
            try:
                # Prefer the first window reference
                w = webview.windows[0] if hasattr(webview, 'windows') and webview.windows else None
            except Exception:
                w = None

            if w is not None:
                # Call the JS helper if present
                try:
                    self.log_event('show_admin_panel invoked; requesting webview to open Admin panel')
                except Exception:
                    pass
                w.evaluate_js('window.showAdminPanel && window.showAdminPanel()')
                return {"success": True, "message": "Admin panel open command sent"}
            else:
                return {"success": False, "error": "No active webview window"}
        except Exception as e:
            return {"success": False, "error": str(e)}

    def close_app(self):
        """Close the application and destroy the webview window."""
        try:
            try:
                w = webview.windows[0] if hasattr(webview, 'windows') and webview.windows else None
                if w is not None:
                    try:
                        w.destroy()
                    except Exception:
                        pass
            except Exception:
                pass
        finally:
            # Ensure process exit as a fallback
            os._exit(0)

    def minimize_window(self):
        try:
            w = webview.windows[0] if hasattr(webview, 'windows') and webview.windows else None
            if w is not None:
                try:
                    w.minimize()
                    return {"success": True}
                except Exception as e:
                    return {"success": False, "error": str(e)}
            return {"success": False, "error": "No active webview window"}
        except Exception as e:
            return {"success": False, "error": str(e)}

    def restore_window(self):
        try:
            w = webview.windows[0] if hasattr(webview, 'windows') and webview.windows else None
            if w is not None:
                try:
                    w.restore()
                    return {"success": True}
                except Exception as e:
                    return {"success": False, "error": str(e)}
            return {"success": False, "error": "No active webview window"}
        except Exception as e:
            return {"success": False, "error": str(e)}

    def hide_admin_panel(self):
        """Request the webview to close the Admin panel (if available in JS)."""
        try:
            try:
                w = webview.windows[0] if hasattr(webview, 'windows') and webview.windows else None
            except Exception:
                w = None

            if w is not None:
                try:
                    self.log_event('hide_admin_panel invoked; requesting webview to close Admin panel')
                except Exception:
                    pass
                w.evaluate_js('window.hideAdminPanel && window.hideAdminPanel()')
                return {"success": True, "message": "Admin panel close command sent"}
            else:
                return {"success": False, "error": "No active webview window"}
        except Exception as e:
            return {"success": False, "error": str(e)}
        
    # --- Admin helpers: Steam path management ---
    def get_steam_path(self):
        try:
            return self.config.get("steam_path", "")
        except Exception:
            return ""

    def set_steam_path(self, path):
        try:
            if not isinstance(path, str) or not path:
                return {"success": False, "error": "Invalid path"}
            self.config["steam_path"] = path
            self.save_config()
            return {"success": True, "steam_path": path}
        except Exception as e:
            return {"success": False, "error": str(e)}

    def browse_steam_path(self):
        """Open a folder picker to select Steam path and save it."""
        try:
            try:
                import tkinter as tk
                from tkinter import filedialog
            except Exception as e:
                return {"success": False, "error": f"UI not available: {e}"}

            root = None
            try:
                root = tk.Tk()
                root.withdraw()
                folder = filedialog.askdirectory(title="Select Steam folder")
            finally:
                try:
                    if root is not None:
                        root.destroy()
                except Exception:
                    pass

            if folder:
                self.config["steam_path"] = folder
                self.save_config()
                return {"success": True, "steam_path": folder}
            else:
                return {"success": False, "error": "Selection cancelled"}
        except Exception as e:
            return {"success": False, "error": str(e)}

    def auto_detect_steam_path(self):
        """Attempt to detect Steam installation directory on Windows."""
        try:
            candidates = []
            try:
                program_files_x86 = os.environ.get('PROGRAMFILES(X86)') or os.environ.get('ProgramFiles(x86)')
                program_files = os.environ.get('PROGRAMFILES') or os.environ.get('ProgramFiles')
                if program_files_x86:
                    candidates.append(os.path.join(program_files_x86, 'Steam'))
                if program_files:
                    candidates.append(os.path.join(program_files, 'Steam'))
                # Common alternative
                candidates.append(os.path.join('C:\\', 'Program Files (x86)', 'Steam'))
                candidates.append(os.path.join('C:\\', 'Program Files', 'Steam'))
            except Exception:
                pass

            def is_valid_steam_dir(path):
                try:
                    return os.path.isdir(path) and os.path.isfile(os.path.join(path, 'steam.exe'))
                except Exception:
                    return False

            detected = None
            for c in candidates:
                if is_valid_steam_dir(c):
                    detected = c
                    break

            if detected:
                self.config["steam_path"] = detected
                self.save_config()
                return {"success": True, "steam_path": detected}
            else:
                return {"success": False, "error": "Steam not found in common locations"}
        except Exception as e:
            return {"success": False, "error": str(e)}

    # --- Admin helpers: Data views ---
    def view_license_history(self):
        try:
            history = self.config.get('license_key_history', [])
            return {"success": True, "count": len(history), "history": history}
        except Exception as e:
            return {"success": False, "error": str(e), "history": []}

    def view_system_logs(self, max_lines: int = 200):
        """Return recent system logs if available. Placeholder if none."""
        try:
            log_paths = [
                os.path.join(os.getcwd(), 'system.log'),
                os.path.join(os.getcwd(), 'logs', 'system.log'),
            ]
            contents = []
            for p in log_paths:
                if os.path.isfile(p):
                    try:
                        with open(p, 'r', encoding='utf-8', errors='ignore') as f:
                            lines = f.readlines()
                            contents = lines[-max_lines:]
                            break
                    except Exception:
                        continue
            return {"success": True, "logs": contents}
        except Exception as e:
            return {"success": False, "error": str(e), "logs": []}

    def log_event(self, message: str = '', level: str = 'INFO'):
        """Append a log event to system.log with an ISO UTC timestamp."""
        try:
            ts = datetime.now(timezone.utc).strftime('%Y-%m-%dT%H:%M:%S.%fZ')
            line = f"[{ts}] [{level}] {message}\n"
            try:
                with open('system.log', 'a', encoding='utf-8') as f:
                    f.write(line)
            except Exception:
                # Fallback: try logs directory
                try:
                    os.makedirs('logs', exist_ok=True)
                    with open(os.path.join('logs', 'system.log'), 'a', encoding='utf-8') as f:
                        f.write(line)
                except Exception:
                    pass
            try:
                print(line.strip())
            except Exception:
                pass
            return {"success": True}
        except Exception as e:
            try:
                print(f"Logging failed: {e}")
            except Exception:
                pass
            return {"success": False, "error": str(e)}

    def close_steam_processes(self):
        """Attempt to close Steam related processes."""
        try:
            chk = self._require_admin()
            if chk:
                return chk
            targets = {"steam.exe", "steamservice.exe", "steamwebhelper.exe"}
            killed = []
            errors = []
            for proc in psutil.process_iter(['pid', 'name']):
                try:
                    name = (proc.info.get('name') or '').lower()
                    if name in targets:
                        try:
                            proc.terminate()
                            try:
                                proc.wait(timeout=5)
                            except Exception:
                                pass
                            killed.append({"pid": proc.pid, "name": name})
                        except Exception as e:
                            errors.append({"pid": proc.pid, "name": name, "error": str(e)})
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            return {"success": True, "terminated": killed, "errors": errors}
        except Exception as e:
            return {"success": False, "error": str(e)}

    # --- Admin session auth helpers ---
    def _require_admin(self):
        try:
            now = time.time()
            if getattr(self, 'admin_session_expiry', 0) and now < self.admin_session_expiry:
                return None
            return {"success": False, "error": "Admin session not authenticated"}
        except Exception:
            return {"success": False, "error": "Admin session check failed"}

    def is_admin_session(self):
        try:
            now = time.time()
            ok = getattr(self, 'admin_session_expiry', 0) and now < self.admin_session_expiry
            return {"success": True, "authenticated": bool(ok), "expires_in": max(0, int(self.admin_session_expiry - now))}
        except Exception as e:
            return {"success": False, "error": str(e), "authenticated": False}

    def verify_admin_password(self, password: str):
        try:
            # Enforce password requirement (default True)
            require_pwd = self.config.get('require_admin_password', True)
            if require_pwd:
                if not isinstance(password, str) or not password:
                    return {"success": False, "error": "Password required"}
                calc = hashlib.sha256(password.encode('utf-8')).hexdigest()
                if calc != ADMIN_PASSWORD_HASH:
                    return {"success": False, "error": "Invalid password"}
            else:
                # When password requirement is disabled, allow without verification
                pass

            # Start/refresh session
            self.admin_session_expiry = time.time() + self.admin_session_timeout
            return {"success": True, "message": "Authenticated", "timeout": self.admin_session_timeout}
        except Exception as e:
            return {"success": False, "error": str(e)}

    def logout_admin_session(self):
        try:
            self.admin_session_expiry = 0
            return {"success": True}
        except Exception as e:
            return {"success": False, "error": str(e)}

    # --- Admin: Devtools / Debug toggle ---
    def get_debug_enabled(self):
        try:
            return {"success": True, "enabled": bool(self.config.get('debug_enabled', False))}
        except Exception as e:
            return {"success": False, "error": str(e), "enabled": False}

    def set_debug_enabled(self, enabled: bool):
        try:
            chk = self._require_admin()
            if chk:
                return chk
            self.config['debug_enabled'] = bool(enabled)
            try:
                self.save_config()
                return {"success": True, "enabled": self.config['debug_enabled']}
            except Exception as e:
                self.log_event(f'Failed to save config in set_debug_enabled: {e}', level='ERROR')
                return {"success": False, "error": f"Failed to save config: {e}"}
        except Exception as e:
            return {"success": False, "error": str(e)}

    def restart_app(self):
        """Restart the application to apply settings like debug/devtools."""
        try:
            chk = self._require_admin()
            if chk:
                return chk

            self.log_event('Restarting application...')

            # Close the current window gracefully if possible
            try:
                w = webview.windows[0] if hasattr(webview, 'windows') and webview.windows else None
                if w:
                    w.destroy()
            except Exception:
                pass # Proceed with restart even if window closing fails

            # Relaunch the application
            # Note: This replaces the current process
            executable = sys.executable
            script_path = os.path.abspath(sys.argv[0])
            
            # For unfrozen script, it's 'python.exe steam_tools_webview.py'
            # os.execv needs the first arg to be the program name itself
            if not getattr(sys, 'frozen', False):
                # In a standard Python environment, sys.executable is the python interpreter
                # and sys.argv[0] is the script name. The command is `python script.py`
                os.execv(executable, [executable.split(os.path.sep)[-1]] + [script_path] + sys.argv[1:])
            else:
                # In a frozen app (e.g., PyInstaller), sys.executable is the app itself
                os.execv(executable, [executable.split(os.path.sep)[-1]] + sys.argv[1:])
            
            # Fallback exit
            os._exit(0)

        except Exception as e:
            self.log_event(f'Restart failed: {e}', level='ERROR')
            return {"success": False, "error": str(e)}

    def is_admin_password_set(self):
        try:
            # Fixed password is always set in this build
            return {"success": True, "set": True}
        except Exception as e:
            return {"success": False, "error": str(e), "set": False}
    
    def remove_hid_dll(self):
        """Attempt to remove hid.dll from the Steam directory (if present)."""
        try:
            chk = self._require_admin()
            if chk:
                return chk
            steam_path = self.config.get('steam_path') or ''
            if not steam_path or not os.path.isdir(steam_path):
                return {"success": False, "error": "Steam path not configured"}

            candidates = [
                os.path.join(steam_path, 'hid.dll'),
                os.path.join(steam_path, 'steamapps', 'hid.dll'),
            ]
            removed = []
            for c in candidates:
                try:
                    if os.path.isfile(c):
                        os.remove(c)
                        removed.append(c)
                except Exception:
                    continue

            # If not found in candidates, do a shallow search under Steam root
            if not removed:
                for root, dirs, files in os.walk(steam_path):
                    if 'hid.dll' in files:
                        try:
                            fp = os.path.join(root, 'hid.dll')
                            os.remove(fp)
                            removed.append(fp)
                            break
                        except Exception:
                            pass

            if removed:
                return {"success": True, "removed": removed}
            else:
                return {"success": False, "error": "hid.dll not found"}
        except Exception as e:
            return {"success": False, "error": str(e)}

    def reset_stplug_folder(self):
        """Remove the stplug-in folder under the Steam directory, if present."""
        try:
            chk = self._require_admin()
            if chk:
                return chk
            steam_path = self.config.get('steam_path') or ''
            if not steam_path or not os.path.isdir(steam_path):
                return {"success": False, "error": "Steam path not configured"}
            target = os.path.join(steam_path, 'stplug-in')
            if os.path.isdir(target):
                try:
                    shutil.rmtree(target)
                    return {"success": True, "removed": target}
                except Exception as e:
                    return {"success": False, "error": f"Failed to remove: {e}"}
            else:
                return {"success": False, "error": "stplug-in folder not found"}
        except Exception as e:
            return {"success": False, "error": str(e)}

    def reset_all_data(self):
        """Reset application configuration to defaults."""
        try:
            chk = self._require_admin()
            if chk:
                return chk
            self.config = self.get_default_config()
            self.save_config()
            return {"success": True, "message": "Configuration reset to defaults"}
        except Exception as e:
            return {"success": False, "error": str(e)}

    def change_admin_password(self, new_password: str):
        """Set or change the admin password (stored as a hash in config)."""
        try:
            # Password is fixed in this build; changing is disabled.
            return {"success": False, "error": "Admin password is fixed in this build"}
        except Exception as e:
            return {"success": False, "error": str(e)}
        
    def load_config(self):
        """Load configuration from file"""
        # Config file should be in the same directory as the script/executable
        if getattr(sys, 'frozen', False):
            # The application is frozen
            base_dir = os.path.dirname(sys.executable)
        else:
            # The application is not frozen
            base_dir = os.path.dirname(os.path.abspath(__file__))
        
        config_path = os.path.join(base_dir, 'config.json')
        try:
            if os.path.exists(config_path):
                with open(config_path, 'r') as f:
                    cfg = json.load(f)
                    # Remove any persisted admin password fields; we don't use config for password
                    cfg.pop('admin_password_hash', None)
                    cfg.pop('admin_password_updated_at', None)
                    return cfg
            else:
                return self.get_default_config()
        except Exception:
            return self.get_default_config()
    
    def get_default_config(self):
        """Get default configuration"""
        return {
            "steam_path": "",
            "license_key_history": [],
            "last_used": {"license_key": "", "app_info": {}},
            # Disable developer tools & pywebview debug by default
            "debug_enabled": False
        }
    
    def save_config(self):
        """Save configuration to file"""
        try:
            with open("config.json", 'w') as f:
                json.dump(self.config, f, indent=2)
        except Exception:
            pass
    
    def init_keyauth(self):
        """Initialize KeyAuth application"""
        try:
            try:
                from keyauth_config import KEYAUTH_CONFIG
                from keyauth import api
                self.keyauth_app = api(
                    name=KEYAUTH_CONFIG["name"],
                    ownerid=KEYAUTH_CONFIG["ownerid"],
                    secret=KEYAUTH_CONFIG["secret"],
                    version=KEYAUTH_CONFIG["version"],
                    hash_to_check=KEYAUTH_CONFIG.get("hash_to_check", "")
                )
                print("KeyAuth initialized successfully")
            except ImportError:
                print("KeyAuth config not found - running in demo mode")
                self.keyauth_app = None
        except Exception as e:
            print(f"KeyAuth initialization failed: {e}")
            self.keyauth_app = None

    def get_hwid(self):
        """Generate hardware ID"""
        try:
            machine_id = platform.node() + platform.machine() + platform.processor()
            hwid = hashlib.md5(machine_id.encode()).hexdigest()
            formatted_hwid = '-'.join([hwid[i:i+4].upper() for i in range(0, 16, 4)])
            return formatted_hwid
        except Exception:
            return "ERROR-HWID-GENERATION"

    def is_admin(self):
        """Check if running as administrator"""
        try:
            return ctypes.windll.shell32.IsUserAnAdmin()
        except:
            return False

    def is_steam_running(self):
        """Check if Steam is currently running"""
        try:
            for proc in psutil.process_iter(['pid', 'name']):
                if proc.info['name'] and proc.info['name'].lower() in ['steam.exe', 'steamwebhelper.exe', 'steamservice.exe']:
                    return True
            return False
        except Exception:
            return False

    def close_steam_processes(self):
        """Close all Steam-related processes"""
        try:
            steam_processes = []
            for proc in psutil.process_iter(['pid', 'name']):
                if proc.info['name'] and proc.info['name'].lower() in ['steam.exe', 'steamwebhelper.exe', 'steamservice.exe']:
                    steam_processes.append(proc)
            
            for proc in steam_processes:
                try:
                    proc.terminate()
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    pass
            
            time.sleep(2)
            
            for proc in steam_processes:
                try:
                    if proc.is_running():
                        proc.kill()
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    pass
            
            return {"success": True, "count": len(steam_processes)}
        except Exception as e:
            return {"success": False, "error": str(e)}

    def validate_license_key(self, license_key):
        """Validate license key format"""
        if not license_key:
            return {"valid": False, "message": "License key cannot be empty"}
        
        license_key = license_key.replace(" ", "").replace("-", "").upper()
        
        if len(license_key) != 16:
            return {"valid": False, "message": "License key must be 16 characters long"}
        
        if not license_key.isalnum():
            return {"valid": False, "message": "License key must contain only letters and numbers"}
        
        formatted_key = '-'.join([license_key[i:i+4] for i in range(0, 16, 4)])
        
        return {"valid": True, "message": "License key format is valid", "formatted_key": formatted_key}

    def activate_license(self, license_key):
        """Activate license key"""
        try:
            validation = self.validate_license_key(license_key)
            if not validation["valid"]:
                return {"success": False, "message": validation["message"]}
            
            formatted_key = validation["formatted_key"]
            
            if self.keyauth_app:
                try:
                    self.keyauth_app.license(formatted_key)
                    self.config["last_used"]["license_key"] = formatted_key
                    self.save_config()
                    return {
                        "success": True, 
                        "message": "License activated successfully!",
                        "license_key": formatted_key
                    }
                except Exception as keyauth_error:
                    return {
                        "success": False, 
                        "message": f"License activation failed: {str(keyauth_error)}"
                    }
            else:
                # Demo mode
                time.sleep(1)
                self.config["last_used"]["license_key"] = formatted_key
                self.save_config()
                return {
                    "success": True, 
                    "message": "License activated successfully! (Demo Mode)",
                    "license_key": formatted_key
                }
                
        except Exception as e:
            return {"success": False, "message": f"Activation error: {str(e)}"}

    def get_connection_status(self):
        """Get connection status to license server"""
        try:
            response = requests.get("https://httpbin.org/status/200", timeout=5)
            latency = response.elapsed.total_seconds() * 1000
            
            return {
                "connected": True,
                "latency": f"{latency:.0f}ms",
                "server": "auth.steamtools.com",
                "uptime": "99.9%"
            }
        except Exception:
            return {
                "connected": False,
                "error": "Connection failed",
                "latency": "N/A",
                "server": "auth.steamtools.com",
                "uptime": "N/A"
            }

    def get_system_info(self):
        """Get system information"""
        try:
            return {
                "platform": platform.platform(),
                "system": platform.system(),
                "machine": platform.machine(),
                "is_admin": self.is_admin(),
                "steam_running": self.is_steam_running()
            }
        except Exception as e:
            return {"error": f"Failed to get system info: {str(e)}"}

    def close_app(self):
        """Close the application"""
        try:
            import webview
            import os
            import threading
            import time
            # Prefer a graceful shutdown of the webview window
            try:
                webview.destroy_window()
                # In case destroy_window does not take effect from this thread,
                # schedule a forced exit shortly after as a fallback.
                def _force_kill_later():
                    try:
                        time.sleep(0.5)
                        os._exit(0)
                    except Exception:
                        pass

                threading.Thread(target=_force_kill_later, daemon=True).start()
                return {"success": True}
            except Exception:
                # Fall back to hard exit if graceful close is unavailable
                pass
        
            os._exit(0)
        except Exception as e:
            return {"success": False, "error": str(e)}

    def minimize_app(self):
        """Minimize the application window"""
        try:
            # Try to minimize using Windows API
            import ctypes
            from ctypes import wintypes

            # Get the current window handle
            hwnd = ctypes.windll.kernel32.GetConsoleWindow()
            if hwnd != 0:
                # Try to minimize the window
                ctypes.windll.user32.ShowWindow(hwnd, 6)  # SW_MINIMIZE = 6
                return {"success": True, "message": "Window minimized"}
            else:
                # Alternative approach - try to find the webview window
                def enum_windows_proc(hwnd, _):
                    if ctypes.windll.user32.IsWindowVisible(hwnd):
                        length = ctypes.windll.user32.GetWindowTextLengthW(hwnd)
                        if length > 0:
                            buffer = ctypes.create_unicode_buffer(length + 1)
                            ctypes.windll.user32.GetWindowTextW(hwnd, buffer, length + 1)
                            if "Steam Tools" in buffer.value:
                                ctypes.windll.user32.ShowWindow(hwnd, 6)  # SW_MINIMIZE
                                return False  # Stop enumeration
                    return True

                EnumWindowsProc = ctypes.WINFUNCTYPE(ctypes.c_bool, wintypes.HWND, wintypes.LPARAM)
                ctypes.windll.user32.EnumWindows(EnumWindowsProc(enum_windows_proc), 0)

                return {"success": True, "message": "Minimize attempted"}

        except Exception as e:
            return {"success": False, "error": str(e), "message": "Minimize not supported"}

def start_borderless_webview():
    """Start the borderless webview application"""

    # Create API instance
    api = SteamToolsAPI()

    # Store window reference globally for window operations
    global app_window
    app_window = None
    
    # Get screen dimensions for optimal sizing
    try:
        import tkinter as tk
        root = tk.Tk()
        screen_width = root.winfo_screenwidth()
        screen_height = root.winfo_screenheight()
        root.destroy()
        
        # Calculate optimal size (80% of screen, minimum 900x700)
        app_width = max(900, int(screen_width * 0.8))
        app_height = max(700, int(screen_height * 0.8))
        
        # Calculate center position
        pos_x = (screen_width - app_width) // 2
        pos_y = (screen_height - app_height) // 2
        
    except Exception:
        app_width, app_height = 900, 700
        pos_x, pos_y = 100, 100
    
    print(f"Starting Steam Tools Borderless WebView...")
    print(f"Window size: {app_width}x{app_height}")
    print(f"Window position: {pos_x},{pos_y}")
    print(f"Config loaded: {api.config}")
    print(f"Debug enabled in config: {api.config.get('debug_enabled', 'NOT_FOUND')}")
    
    # Get the HTML file path
    html_file = os.path.join(os.path.dirname(__file__), 'web', 'steam_license_webview.html')
    
    # Create TRUE borderless window (no title bar at all)
    app_window = webview.create_window(
        'Steam Tools - License Activation',
        html_file,
        width=app_width,
        height=app_height,
        x=pos_x,
        y=pos_y,
        resizable=False,
        fullscreen=False,
        minimized=False,
        on_top=False,
        frameless=True,  # This removes the title bar completely
        easy_drag=False,  # We'll use custom drag region instead
        background_color='#0e1419',  # Steam dark background
        text_select=False,  # Disable text selection for app-like feel
        js_api=api  # Expose Python API to JavaScript
    )
    
    # Start the webview (this will be truly borderless)
    # Use config-based debug flag to control devtools / inspection availability
    debug_flag = bool(api.config.get('debug_enabled', False))
    print(f"Debug flag being passed to webview.start(): {debug_flag}")
    webview.start(debug=debug_flag, http_server=True, http_port=0)

if __name__ == '__main__':
    start_borderless_webview()
